# Contributing to TypingMind Proxy Plugins

Thank you for your interest in enhancing the TypingMind Proxy! Here's how you can create powerful plugins and contribute to this project.

## Guidelines for Plugin Development

1. **Fork the Repository:** Begin by forking the main TypingMind Proxy repository. This creates your working copy.
2. **Clone Locally:** Use `git clone your-fork-url` to download your fork to your development environment.
3. **Create a Branch:** Isolate your changes with `git checkout -b descriptive-branch-name main`.
4. **Develop Your Plugin:** Build new features, address bugs, and optimize your plugin's functionality.
5. **Thorough Testing:** Ensure your plugin works as intended and doesn't introduce regressions.
6. **Meaningful Commits:** Use the "Conventional Commits" VSCode extension (Identifier: vivaxy.vscode-conventional-commits) for clear and structured commit messages.
7. **Push Your Changes:** Send your work to your fork with `git push origin descriptive-branch-name`.
8. **Submit a Pull Request:** From your fork on GitHub, create a pull request to merge your changes into the main project.

## Reporting Issues & Suggesting Features

- **Bug Reports:** If you discover a bug, please open an issue. Provide a detailed description, steps to reproduce, and your environment information.
- **Feature Requests:** Have an idea for a new plugin or enhancement? Open an issue and describe your proposed feature.

## Community Guidelines

- **Clarity and Conciseness:** Communicate clearly, explaining concepts as needed for broad understanding.
- **Adhere to Style Guides:** Match the existing TypingMind Proxy coding style for consistency.
- **Be Respectful:** Maintain a constructive and welcoming environment for all contributors.

## Licensing

By contributing, you agree to release your plugin under the same license as TypingMind Proxy. See the LICENSE file for details.

## Appreciation

Your contributions make TypingMind Proxy a more robust and valuable tool. Thank you for being a part of this community!
