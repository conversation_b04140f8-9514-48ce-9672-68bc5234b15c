name: Docker Image CI

on:
  push:
    branches: ['main']

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GH_TOKEN }}

      - name: Build the Docker image
        run: docker build . --file Dockerfile --tag ghcr.io/${{ secrets.GH_REPOSITORY }}:latest

      - name: Push the Docker image
        run: docker push ghcr.io/${{ secrets.GH_REPOSITORY }}:latest
