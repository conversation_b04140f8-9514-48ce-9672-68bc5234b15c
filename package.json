{"name": "typingmind-proxy", "version": "1.2.1", "description": "TypingMind Proxy", "main": "index.ts", "scripts": {"dev": "tsx watch --clear-screen=false src/index.ts | pino-pretty", "build": "rimraf dist && tsup", "start": "tsx dist/index.js", "clean": "rimraf dist coverage", "lint": "eslint", "lint:fix": "eslint --fix", "prettier": "prettier --config .prettierrc --write .", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest --coverage", "prepare": "husky install", "release": "release-it"}, "dependencies": {"@asteasolutions/zod-to-openapi": "^7.3.0", "@mozilla/readability": "^0.5.0", "@notionhq/client": "^2.2.15", "@types/got": "^9.6.12", "@types/jsdom": "^21.1.6", "body-parser": "^1.20.2", "cheerio": "^1.0.0-rc.12", "cors": "^2.8.5", "docx": "^9.1.0", "dotenv": "^16.4.5", "envalid": "^8.0.0", "exceljs": "^4.4.0", "express": "^4.19.2", "express-rate-limit": "^7.2.0", "fs": "^0.0.1-security", "got": "^14.2.1", "helmet": "^7.1.0", "http-status-codes": "^2.3.0", "jsdom": "^26.0.0", "node-cron": "^3.0.3", "path": "^0.12.7", "pino-http": "^10.4.0", "pptxgenjs": "^3.12.0", "swagger-ui-express": "^5.0.0", "uuidv4": "^6.2.13", "youtube-transcript": "^1.1.0", "zod": "^3.22.4"}, "devDependencies": {"@commitlint/cli": "^19.0.3", "@commitlint/config-conventional": "^19.0.3", "@release-it/conventional-changelog": "^8.0.1", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node-cron": "^3.0.11", "@types/supertest": "^6.0.2", "@types/swagger-ui-express": "^4.1.6", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "@vitest/coverage-v8": "^1.3.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^12.0.0", "husky": "^9.0.11", "lint-staged": "^15.2.2", "pino-pretty": "^13.0.0", "prettier": "^3.2.5", "release-it": "^17.1.1", "rimraf": "^5.0.5", "supertest": "^7.0.0", "tsup": "^8.0.2", "tsx": "^4.7.1", "typescript": "^5.3.3", "vite-tsconfig-paths": "^4.3.1", "vitest": "^1.3.1"}, "lint-staged": {"**/*": "npm run prettier", "**/*.{js,ts}": "npm run lint:fix"}, "author": "<PERSON>", "repository": "travis-thuanle/typingmind-proxy", "license": "MIT"}