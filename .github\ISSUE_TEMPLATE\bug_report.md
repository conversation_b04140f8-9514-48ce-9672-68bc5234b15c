---
name: TypingMind Proxy Bug Report
about: Found something not working right? Help us fix it!
title: ''
labels: 'bug'
assignees: ''
---

**Describe the Bug**

- Provide a short, descriptive summary of the unexpected behavior.
- Be as specific as possible about what went wrong.

**Steps to Reproduce**

1. **Plugin:** Specify the name and version of the plugin where you encountered the bug.
2. **Actions:** List the exact steps a developer needs to take within the plugin to trigger the bug.
3. **Configuration:** If relevant, describe any specific settings or configurations related to the issue.

**Expected Behavior**

- Clearly describe how the plugin _should_ function in the scenario where the bug occurs.

**Environment Information**

- **TypingMind Proxy Version:**
- **Plugin Name and Version:**
- **Operating System:**
- **Browser (if applicable):**
- **Other Relevant Software:** (List any other tools or versions that might be relevant)

**Visuals (Optional but Helpful)**

- **Screenshots:** Capture the bug in action if possible.
- **Code Snippets:** Include small blocks of code if it directly demonstrates the problem.

**Additional Context**

- **Error Messages:** Any specific error logs or messages.
- **Impact:** Does the bug prevent a task? Does it cause data issues? Explain the severity.

**Thank you for helping us make TypingMind Proxy even better!**
