{"env": {"es2021": true, "node": true}, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint", "simple-import-sort", "prettier"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"@typescript-eslint/no-explicit-any": "off", "simple-import-sort/imports": "error", "simple-import-sort/exports": "error", "prettier/prettier": "error"}}